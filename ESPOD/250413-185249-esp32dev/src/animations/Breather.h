#ifndef BREATHER_H
#define BREATHER_H

#include <Arduino.h>

#define LED_PIN 2          // Onboard LED on GPIO 2
#define LED_BRIGHTNESS 10  // Brightness (0-255, lower = dimmer)
#define LEDC_CHANNEL 0     // PWM channel (0-15 for ESP32)
#define LEDC_FREQ 5000     // PWM frequency in Hz
#define LEDC_RES 8         // PWM resolution in bits

void breathe();
void Breather(void *pvParameters);
void initBreather();

#endif
