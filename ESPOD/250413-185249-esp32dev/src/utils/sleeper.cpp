#include "sleeper.h"
#include "../ui/Buttons.h"

void modifyJoystickForDeepSleep() {
  // Configure all joystick pins for deep sleep
  // Set all button pins as RTC GPIO inputs with pull-up for wake-up capability

  gpio_num_t joystickPins[] = {
    BTN_UP_PIN, BTN_DOWN_PIN, BTN_LEFT_PIN, BTN_RIGHT_PIN,
    BTN_CENTER_PIN, BTN_SET_PIN, BTN_RST_PIN
  };

  for (int i = 0; i < 7; i++) {
    if (rtc_gpio_is_valid_gpio(joystickPins[i])) {
      rtc_gpio_deinit(joystickPins[i]);
      rtc_gpio_set_direction_in_sleep(joystickPins[i], RTC_GPIO_MODE_INPUT_ONLY);
      rtc_gpio_pullup_en(joystickPins[i]);
    }
  }
}

void modifyJoystickForNormalUse() {
  // Deinit RTC GPIO function for all joystick pins, so they go back to normal GPIO control
  gpio_num_t joystickPins[] = {
    BTN_UP_PIN, BTN_DOWN_PIN, BTN_LEFT_PIN, BTN_RIGHT_PIN,
    BTN_CENTER_PIN, BTN_SET_PIN, BTN_RST_PIN
  };

  for (int i = 0; i < 7; i++) {
    if (rtc_gpio_is_valid_gpio(joystickPins[i])) {
      rtc_gpio_deinit(joystickPins[i]);
    }
  }
}

void sleepWell() {
  Serial.println("I am sleeping now..");
  detachButtons();
  delay(500);
  modifyJoystickForDeepSleep();

  // Wake up on RST button press (GPIO 33, active low)
  esp_sleep_enable_ext0_wakeup(BTN_RST_PIN, 0);  // wake if RST button goes LOW
  esp_deep_sleep_start();
}
