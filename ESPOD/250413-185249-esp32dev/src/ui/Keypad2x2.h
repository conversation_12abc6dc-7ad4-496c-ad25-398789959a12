#ifndef KEYPAD2X2_H
#define KEYPAD2X2_H

#include "driver/gpio.h"
#include "esp_timer.h"

extern volatile bool interruptFlag;

typedef void (*KeypadCallback)(char key);
typedef void (*LongPressCallback)(char key);

class Keypad2x2 {
  public:
    Keypad2x2(gpio_num_t rowPins[2], gpio_num_t colPins[2], const char keymap[2][2], uint32_t longPressTimeoutMs = 2000);

    void begin();
    void destroy();
    void setCallback(KeypadCallback cb);
    void setLongPressCallback(LongPressCallback cb);
    char getLastKey();

  private:
    gpio_num_t _rowPins[2];
    gpio_num_t _colPins[2];
    const char (*_keymap)[2];

    volatile char _lastKey;
    int _lastRow, _lastCol;
    int64_t _lastInterruptTime;
    bool _longPressDetected;
    uint32_t _longPressTimeout;

    esp_timer_handle_t _longPressTimer;

    KeypadCallback _callback;
    LongPressCallback _longPressCallback;

    void initKeyPadPins();
    void scanKeypad();
    void handleKeyPress();
    void handleKeyRelease();

    static void IRAM_ATTR handleInterruptWrapper();
    void IRAM_ATTR handleInterrupt();
    static void IRAM_ATTR longPressTimerCallback(void* arg);
};

#endif
