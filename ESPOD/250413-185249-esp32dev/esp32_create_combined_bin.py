Import("env")

# This script enables ESP-IDF components to be used with Arduino framework
# It's a workaround for using both Arduino and ESP-IDF in the same project

env.Append(
    CPPDEFINES=[
        ("CONFIG_BT_ENABLED", "1"),
        ("CONFIG_BT_CLASSIC_ENABLED", "1"),
        ("CONFIG_BT_A2DP_ENABLE", "1"),
        ("CONFIG_BT_A2DP_SOURCE_ENABLE", "1"),
        ("CONFIG_BT_SPP_ENABLED", "0"),
        ("CONFIG_BT_BLE_ENABLED", "0"),
    ]
)

print("ESP-IDF Bluetooth components enabled for Arduino framework")
