#ifndef BUTTONS_H
#define BUTTONS_H

#include <Arduino.h>

// Button identifiers for 7-button joystick
typedef enum {
    BTN_UP = 0,
    BTN_DOWN,
    BTN_LEFT,
    BTN_RIGHT,
    BTN_CENTER,
    BTN_SET,
    BTN_RST,
    BTN_COUNT  // Total number of buttons
} button_id_t;

// Callback function types
typedef void (*ButtonCallback)(button_id_t button);
typedef void (*LongPressCallback)(button_id_t button);

// Pin assignments for 7-button joystick
extern gpio_num_t buttonPins[BTN_COUNT];

void initButtons(ButtonCallback onButtonPress, LongPressCallback onLongPress);
void detachButtons();

#endif