#ifndef SLEEPER_H
#define SLEEPER_H

#include <Arduino.h>
#include "driver/rtc_io.h"

// 7-button joystick pins (same as defined in Buttons.cpp)
#define BTN_UP_PIN    GPIO_NUM_12
#define BTN_DOWN_PIN  GPIO_NUM_13
#define BTN_LEFT_PIN  GPIO_NUM_14
#define BTN_RIGHT_PIN GPIO_NUM_27
#define BTN_CENTER_PIN GPIO_NUM_26
#define BTN_SET_PIN   GPIO_NUM_25
#define BTN_RST_PIN   GPIO_NUM_33

void modifyJoystickForDeepSleep();
void modifyJoystickForNormalUse();
void sleepWell();

#endif
