#include "Breather.h"

void breathe() {
  // Serial.println("Breathing..");
  // Set LED brightness via PWM
  ledcWrite(LEDC_CHANNEL, LED_BRIGHTNESS);
  vTaskDelay(1000 / portTICK_PERIOD_MS);  // Delay 1s
  ledcWrite(LEDC_CHANNEL, 0);
  vTaskDelay(1000 / portTICK_PERIOD_MS);  // Delay 1s
}

void Breather(void *pvParameters) {
  // Initialize PWM for LED_PIN
  ledcSetup(LEDC_CHANNEL, LEDC_FREQ, LEDC_RES);
  ledcAttachPin(LED_PIN, LEDC_CHANNEL);

  while (true) {
    breathe();
  }
}

void initBreather() {
  xTaskCreatePinnedToCore(
    Breather,         // Function to run as task
    "Breather",       // Task name
    2048,             // Stack size (in bytes)
    NULL,             // Parameters to pass
    1,                // Task priority
    NULL,             // Task handle
    0                 // Run on core 0
  );
}
