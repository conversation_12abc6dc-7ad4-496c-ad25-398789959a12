#include "Arduino.h"
#include "Keypad2x2.h"
#include "driver/gpio.h"
#include "esp_timer.h"

static Keypad2x2* instancePtr = nullptr;
volatile bool interruptFlag = false;

Keypad2x2::Keypad2x2(gpio_num_t rowPins[2], gpio_num_t colPins[2], const char keymap[2][2], uint32_t longPressTimeoutMs) {
  _rowPins[0] = rowPins[0];
  _rowPins[1] = rowPins[1];
  _colPins[0] = colPins[0];
  _colPins[1] = colPins[1];
  _keymap = keymap;

  _lastKey = 0;
  _lastRow = -1;
  _lastCol = -1;
  _lastInterruptTime = 0;
  _callback = nullptr;
  _longPressCallback = nullptr;
  _longPressDetected = false;
  _longPressTimeout = longPressTimeoutMs * 1000;

  _longPressTimer = nullptr;

  instancePtr = this;
}

void Keypad2x2::begin() {
  initKeyPadPins();

  attachInterrupt(digitalPinToInterrupt(_rowPins[0]), Keypad2x2::handleInterruptWrapper, CHANGE);
  attachInterrupt(digitalPinToInterrupt(_rowPins[1]), Keypad2x2::handleInterruptWrapper, CHANGE);

  esp_timer_create_args_t timer_args = {
    .callback = &Keypad2x2::longPressTimerCallback,
    .arg = this,
    .dispatch_method = ESP_TIMER_TASK,
    .name = "longPressTimer"
  };
  esp_timer_create(&timer_args, &_longPressTimer);
}

void Keypad2x2::destroy() {
  detachInterrupt(digitalPinToInterrupt(_rowPins[0]));
  detachInterrupt(digitalPinToInterrupt(_rowPins[1]));
}

void Keypad2x2::initKeyPadPins() {
  gpio_config_t col_conf = {
    .pin_bit_mask = (1ULL << _colPins[0]) | (1ULL << _colPins[1]),
    .mode = GPIO_MODE_OUTPUT,
    .pull_up_en = GPIO_PULLUP_DISABLE,
    .pull_down_en = GPIO_PULLDOWN_DISABLE,
    .intr_type = GPIO_INTR_DISABLE
  };
  gpio_config(&col_conf);
  gpio_set_level(_colPins[0], 0);
  gpio_set_level(_colPins[1], 0);

  gpio_config_t row_conf = {
    .pin_bit_mask = (1ULL << _rowPins[0]) | (1ULL << _rowPins[1]),
    .mode = GPIO_MODE_INPUT,
    .pull_up_en = GPIO_PULLUP_ENABLE,
    .pull_down_en = GPIO_PULLDOWN_DISABLE,
    .intr_type = GPIO_INTR_ANYEDGE
  };
  gpio_config(&row_conf);
}

void IRAM_ATTR Keypad2x2::handleInterruptWrapper() {
  if (instancePtr) {
    instancePtr->handleInterrupt();
  }
}

void IRAM_ATTR Keypad2x2::handleInterrupt() {
  int64_t now = esp_timer_get_time();
  if (now - _lastInterruptTime < 30000) return;  // 30ms debounce
  _lastInterruptTime = now;

  scanKeypad();
  initKeyPadPins();
}

void Keypad2x2::scanKeypad() {
  bool keyFound = false;

  for (int col = 0; col < 2; col++) {
    gpio_set_level(_colPins[0], 1);
    gpio_set_level(_colPins[1], 1);
    gpio_set_level(_colPins[col], 0);

    for (int row = 0; row < 2; row++) {
      int level = gpio_get_level(_rowPins[row]);

      if (level == 0) {
        if (_lastKey == 0) {
          _lastKey = _keymap[row][col];
          _lastRow = row;
          _lastCol = col;
          _longPressDetected = false;

          esp_timer_stop(_longPressTimer);
          esp_timer_start_once(_longPressTimer, _longPressTimeout);

          handleKeyPress();
        }
        keyFound = true;
      }
    }
  }

  if (!keyFound && _lastKey != 0) {
    esp_timer_stop(_longPressTimer);
    handleKeyRelease();
    _lastKey = 0;
  }
}

void Keypad2x2::handleKeyPress() {
  // nothing now — optionally add press event if needed
}

void Keypad2x2::handleKeyRelease() {
  if (!_longPressDetected && _callback) {
    _callback(_lastKey);
  }
}

void IRAM_ATTR Keypad2x2::longPressTimerCallback(void* arg) {
  Keypad2x2* keypad = static_cast<Keypad2x2*>(arg);

  for (int col = 0; col < 2; col++) {
    gpio_set_level(keypad->_colPins[0], 1);
    gpio_set_level(keypad->_colPins[1], 1);
    gpio_set_level(keypad->_colPins[col], 0);

    for (int row = 0; row < 2; row++) {
      if (gpio_get_level(keypad->_rowPins[row]) == 0) {
        char key = keypad->_keymap[row][col];
        keypad->_longPressDetected = true;

        if (keypad->_longPressCallback) {
          keypad->_longPressCallback(key);
        }
      }
    }
  }
  keypad->initKeyPadPins();
}

char Keypad2x2::getLastKey() {
  char key = _lastKey;
  _lastKey = 0;
  return key;
}

void Keypad2x2::setCallback(KeypadCallback cb) {
  _callback = cb;
}

void Keypad2x2::setLongPressCallback(LongPressCallback cb) {
  _longPressCallback = cb;
}
